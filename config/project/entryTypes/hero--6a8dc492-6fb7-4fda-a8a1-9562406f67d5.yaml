color: null
description: null
fieldLayouts:
  41a9ea28-ddeb-42a0-a279-a12f0d7d4ba0:
    cardThumbAlignment: end
    tabs:
      -
        elementCondition: null
        elements:
          -
            autocapitalize: true
            autocomplete: false
            autocorrect: true
            class: null
            dateAdded: '2025-09-01T10:53:53+00:00'
            disabled: false
            elementCondition: null
            id: null
            includeInCards: false
            inputType: null
            instructions: null
            label: null
            max: null
            min: null
            name: null
            orientation: null
            placeholder: null
            providesThumbs: false
            readonly: false
            required: true
            size: null
            step: null
            tip: null
            title: null
            type: craft\fieldlayoutelements\entries\EntryTitleField
            uid: 149355e9-a4e1-43d7-a0d1-0ea98462c115
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-09-01T10:56:32+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: d62a9951-5dc6-4ad2-9701-6e3d8909e8e9 # Subtitle
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 329c282a-1992-4b5e-a159-a540b0596a62
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-09-01T10:56:32+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 473f06bd-1ddc-40ce-b1e7-0146e69d7c31 # Background Image
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 90668b3a-d429-4303-a09a-32c66faaa877
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-09-01T10:56:32+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 550d8241-819c-42f5-bcf8-47fd9f6016fc # Button Text
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 6754c611-c49f-40c9-9c51-710160f3670f
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-09-01T10:56:32+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 6a337d87-a9f1-46b1-82f8-9fe1e207b016 # ButtonUrl
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 0437f1b3-db69-4079-8b89-6b03f7c16187
            userCondition: null
            warning: null
            width: 100
          -
            dateAdded: '2025-09-01T13:18:23+00:00'
            editCondition: null
            elementCondition: null
            fieldUid: 9f3ea766-b7d6-4c17-b4a1-316173b38ea3 # Info
            handle: null
            includeInCards: false
            instructions: null
            label: null
            providesThumbs: false
            required: false
            tip: null
            type: craft\fieldlayoutelements\CustomField
            uid: 3426f053-f718-4d05-9b8b-2fa4ab8fa441
            userCondition: null
            warning: null
            width: 100
        name: Content
        uid: c5ec662e-e828-4918-bf11-a427cd10a640
        userCondition: null
handle: hero
hasTitleField: true
icon: null
name: Hero
showSlugField: true
showStatusField: true
slugTranslationKeyFormat: null
slugTranslationMethod: site
titleFormat: null
titleTranslationKeyFormat: null
titleTranslationMethod: site
