columnSuffix: null
handle: contentBlocks
instructions: null
name: 'Content Blocks'
searchable: false
settings:
  createButtonLabel: null
  defaultIndexViewMode: cards
  enableVersioning: false
  entryTypes:
    -
      __assoc__:
        -
          - uid
          - 6a8dc492-6fb7-4fda-a8a1-9562406f67d5 # Hero
        -
          - group
          - General
    -
      __assoc__:
        -
          - uid
          - 429ed7aa-cf8e-49e2-b487-55658d151e1e # Studio
        -
          - group
          - General
    -
      __assoc__:
        -
          - uid
          - 8cdc748b-afc1-4bc6-b829-81708741d99a # Maps
        -
          - group
          - General
    -
      __assoc__:
        -
          - uid
          - 263102ca-a344-4186-80bb-b3913f61195b # Services
        -
          - group
          - General
    -
      __assoc__:
        -
          - uid
          - 8e2f8263-22c5-4056-9418-78a531171402 # Testimonials & Pricelist
        -
          - group
          - General
    -
      __assoc__:
        -
          - uid
          - fd53a628-9f7c-47db-afb8-5cfd314a4d79 # Expert
        -
          - group
          - General
  includeTableView: false
  maxEntries: null
  minEntries: null
  pageSize: 50
  propagationKeyFormat: null
  propagationMethod: all
  showCardsInGrid: false
  viewMode: cards
translationKeyFormat: null
translationMethod: site
type: craft\fields\Matrix
