@import 'tailwindcss';

@source '../../';

html, body {
  position: relative;
  overflow-x: hidden;
}

:root {
    --color-light: #EDE4D1;
    --color-dark: #312F2C;
}

@theme {
    --color-light: var(--color-light);
    --color-dark: var(--color-dark);
}

@utility container {
  padding-inline: 1rem;
  margin: 0 auto;
}

h2, .h2 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 900;
    font-size: 43px;   
}

h3, .h3 {
    font-weight: 700;
    font-size: 30px;   
}

h4, .h4 {
    font-weight: 500;
    font-size: 18px;   
}

.btn {
    background: var(--color-light);
    color: var(--color-dark);
    border-radius: 0.5rem;
    padding: 1rem 3rem;
    font-weight: 700;
}

.btn:hover {
    
}

.btn-outline {
    @apply bg-transparent border border-light text-light hover:bg-light hover:text-dark;
}

.embla__container { will-change: transform; }
.embla__slide { scroll-snap-align: start; }
.embla__dot[aria-selected="true"] { background: rgba(255,255,255,.9); }


.progressive-blur-container {
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 100%;
  width: 50%;
  pointer-event: none;
}

.progressive-blur-container > .blur-filter {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
}

.progressive-blur-container > .blur-filter:nth-child(1) {
  backdrop-filter: blur(1px);
  mask: linear-gradient(
    to left,
    rgba(0, 0, 0, 0),
    rgba(0, 0, 0, 1) 10%,
    rgba(0, 0, 0, 1) 30%,
    rgba(0, 0, 0, 0) 40%
  );
}

.progressive-blur-container > .blur-filter:nth-child(2) {
  backdrop-filter: blur(2px);
  mask: linear-gradient(
    to left,
    rgba(0, 0, 0, 0) 10%,
    rgba(0, 0, 0, 1) 20%,
    rgba(0, 0, 0, 1) 40%,
    rgba(0, 0, 0, 0) 50%
  );
}

.progressive-blur-container > .blur-filter:nth-child(3) {
  backdrop-filter: blur(4px);
  mask: linear-gradient(
    to left,
    rgba(0, 0, 0, 0) 15%,
    rgba(0, 0, 0, 1) 30%,
    rgba(0, 0, 0, 1) 50%,
    rgba(0, 0, 0, 0) 60%
  );
}

.progressive-blur-container > .blur-filter:nth-child(4) {
  backdrop-filter: blur(8px);
  mask: linear-gradient(
    to left,
    rgba(0, 0, 0, 0) 20%,
    rgba(0, 0, 0, 1) 40%,
    rgba(0, 0, 0, 1) 60%,
    rgba(0, 0, 0, 0) 70%
  );
}

.progressive-blur-container > .blur-filter:nth-child(5) {
  backdrop-filter: blur(16px);
  mask: linear-gradient(
    to left,
    rgba(0, 0, 0, 0) 40%,
    rgba(0, 0, 0, 1) 60%,
    rgba(0, 0, 0, 1) 80%,
    rgba(0, 0, 0, 0) 90%
  );
}

.progressive-blur-container > .blur-filter:nth-child(6) {
  backdrop-filter: blur(32px);
  mask: linear-gradient(
    to left,
    rgba(0, 0, 0, 0) 60%,
    rgba(0, 0, 0, 1) 80%
  );
}

.progressive-blur-container > .blur-filter:nth-child(7) {
  z-index: 10;
  background-filter: blur(64px);
  mask: linear-gradient(
    to left,
    rgba(0, 0, 0, 0) 70%,
    rgba(0, 0, 0, 1) 100%
  );
}

.nav-link {
  color: #fff;
  text-decoration: none;
  font-weight: 700;
  position: relative;
}

.nav-link:before {
  content: "";
  position: absolute;
  left: 0;
  width: 100%;
  background: #fff;
  height: 1px;
  bottom: 0;
  transform: scaleX(0);
  transition: transform 0.5s cubic-bezier(0.2, 0.8, 0.2, 1);
  transform-origin: right; 
}

.nav-link:hover:before {
  transform: scaleX(1);
  transform-origin: left;
}

.bg {
  backdrop-filter: blur(4px);
  background: rgba(0,0,0,0.35);
}
