import "lenis/dist/lenis.css";
import "../css/index.css";
import "./studio.embla";
import "./services.embla";
import "./testimonialsPricelist.embla";

import Lenis from "lenis";
import Alpine from "alpinejs";
import focus from "@alpinejs/focus";
window.Alpine = Alpine;

Alpine.plugin(focus);
Alpine.start();

/**
 * Accept HMR as per: https://vitejs.dev/guide/api-hmr.html & https://nystudio107.com/docs/vite/
 */
if (import.meta.hot) {
	import.meta.hot.accept(() => {
		console.log("HMR");
	});
}

const lenis = new Lenis({
	autoRaf: true,
});
