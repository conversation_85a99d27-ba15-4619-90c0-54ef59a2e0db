import EmblaCarousel from "embla-carousel";

function initEmbla(section) {
	const viewport = section.querySelector("[data-embla-viewport]");
	const track = section.querySelector("[data-embla-track]");
	if (!viewport || !track) return;

	const slideSelector = "[data-embla-slide]";
	const realSlides = Array.from(track.querySelectorAll(slideSelector));

	const prevBtn = section.querySelector("[data-embla-prev]");
	const nextBtn = section.querySelector("[data-embla-next]");
	const indexEl = section.querySelector("[data-embla-index]");

	const embla = EmblaCarousel(viewport, {
		align: "start",
		slidesToScroll: 1,
		loop: false,
		containScroll: "trimSnaps", // avoids “snap back” on partial slides
		skipSnaps: false,
		slides: slideSelector, // only real slides
	});

	function updateUI() {
		const selected = embla.selectedScrollSnap();
		const total = track.querySelectorAll(
			"[data-embla-slide]:not([aria-hidden=true])",
		).length;
		const display = Math.min(selected + 1, total);

		if (indexEl) indexEl.textContent = `${display}/${total}`;

		const atFirst = selected <= 0;
		const atLast = selected >= total - 1;

		if (prevBtn) {
			prevBtn.toggleAttribute("disabled", atFirst);
			prevBtn.classList.toggle("opacity-30", atFirst);
			prevBtn.classList.toggle("cursor-not-allowed", atFirst);
		}
		if (nextBtn) {
			nextBtn.toggleAttribute("disabled", atLast);
			nextBtn.classList.toggle("opacity-30", atLast);
			nextBtn.classList.toggle("cursor-not-allowed", atLast);
		}
	}

	// Simple button handlers (no clamping needed since ghosts aren’t slides)
	prevBtn?.addEventListener("click", () => embla.scrollPrev());
	nextBtn?.addEventListener("click", () => embla.scrollNext());

	// Debounced re-init after image/layout changes
	let reinitQueued = false;
	const recalc = () => {
		if (reinitQueued) return;
		reinitQueued = true;
		requestAnimationFrame(() => {
			embla.reInit();
			updateUI();
			reinitQueued = false;
		});
	};

	// Re-init when images load (so sizes/snaps are final)
	for (const sl of realSlides) {
		const img = sl.querySelector("img");
		if (!img) continue;
		if (img.complete) {
			// already loaded — still queue one recalculation
			recalc();
		} else {
			img.addEventListener("load", recalc, { once: true });
			img.addEventListener("error", recalc, { once: true });
		}
	}
	// Also after window load (fonts, etc.)
	window.addEventListener("load", recalc);

	embla.on("init", updateUI).on("select", updateUI).on("resize", updateUI);
	updateUI();
}

document.addEventListener("DOMContentLoaded", () => {
	document.querySelectorAll('[data-section="services"]').forEach(initEmbla);
});
