import EmblaCarousel from "embla-carousel";

const clamp = (v, a, b) => Math.min(Math.max(v, a), b);

function initEmbla(root) {
	const vp = root.querySelector("[data-embla-viewport]");
	if (!vp) return;

	const embla = EmblaCarousel(vp, {
		loop: true,
		align: "start",
		containScroll: "trimSnaps",
		dragFree: false,
		slidesToScroll: 1,
	});

	const fills = [...root.querySelectorAll(".embla__fill")];
	const DURATION = Number.parseInt(root.dataset.autoplayMs || "4500", 10);

	let raf = null;
	let startTs = 0;
	let remaining = DURATION;
	let idx = embla.selectedScrollSnap();
	let paused = false;

	const cancel = () => {
		if (raf) cancelAnimationFrame(raf);
		raf = null;
	};
	const widthPct = (i) =>
		fills[i] ? Number.parseFloat(fills[i].style.width || "0") : 0;

	// biome-ignore lint/suspicious/noAssignInExpressions: <explanation>
	// biome-ignore lint/complexity/noForEach: <explanation>
	const resetFills = () => fills.forEach((el) => (el.style.width = "0%"));

	function tick(ts) {
		if (!startTs) startTs = ts;
		const elapsed = ts - startTs;
		const progress = clamp(elapsed / remaining, 0, 1);
		if (fills[idx]) fills[idx].style.width = `${progress * 100}%`;

		if (elapsed >= remaining) {
			embla.scrollNext();
			return;
		}
		raf = requestAnimationFrame(tick);
	}

	function start() {
		cancel();
		idx = embla.selectedScrollSnap();
		resetFills();
		startTs = 0;
		remaining = DURATION;
		raf = requestAnimationFrame(tick);
	}

	function pause() {
		if (paused) return;
		paused = true;
		cancel();
		const w = widthPct(idx);
		remaining = Math.max(1, DURATION * (1 - w / 100));
	}

	function resume() {
		if (!paused) return;
		paused = false;
		startTs = 0;
		raf = requestAnimationFrame(tick);
	}

	embla.on("pointerDown", pause);
	embla.on("dragStart", pause);
	embla.on("settle", resume);

	embla.on("select", start);
	embla.on("reInit", start);

	document.addEventListener("visibilitychange", () => {
		if (document.hidden) pause();
		else resume();
	});

	start();
}

document.addEventListener("DOMContentLoaded", () => {
	document.querySelectorAll("[data-section=studio] .embla").forEach(initEmbla);
});
