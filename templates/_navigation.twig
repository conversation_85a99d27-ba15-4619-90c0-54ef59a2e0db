<!-- Alpine (only if you don't already load it) -->
<script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

<header x-data="{ open: false }" x-init="$watch('open', v => document.body.style.overflow = v ? 'hidden' : '')" @keydown.escape.window="open = false" class="fixed top-0 inset-x-0 pt-4 z-50 px-6 sm:px-12">
	<div
		class="grid grid-cols-2 md:grid-cols-[1fr_auto_1fr] items-center">

		<!-- Left: Mobile menu button -->
		<div class="flex items-center">
			<button
				type="button" class="md:hidden text-white font-bold inline-flex items-center justify-center rounded-full p-2" aria-label="Menü öffnen" :aria-expanded="open" @click="open = true">
				<!-- Hamburger -->
				Menü
			</button>
		</div>

		<!-- Center: Desktop nav -->
		<nav class="hidden md:block">
			<ul class="flex gap-12 justify-center">
				<li>
					<a class="nav-link" href="#">HOME</a>
				</li>
				<li>
					<a class="nav-link" href="#">STUDIO</a>
				</li>
				<li>
					<a class="nav-link" href="#">SERVICES</a>
				</li>
				<li>
					<a class="nav-link" href="#">PREISE</a>
				</li>
				<li>
					<a class="nav-link" href="#">BURHAN TEKIN</a>
				</li>
				<li>
					<a class="nav-link" href="#">LA BIOSTHETIQUE &copy;</a>
				</li>
			</ul>
		</nav>

		<!-- Right: CTA -->
		<div class="flex justify-end">
			{% include "_partials/call-or-mail.twig" with {
          phone: '+43 987 654 321',
          email: '<EMAIL>',
          class: 'btn btn-outline | inline-block'
      } %}
		</div>
	</div>

	<!-- Mobile fullscreen overlay -->
	<div
		class="fixed inset-0 z-[60] md:hidden transition-opacity duration-200" x-cloak :class="open ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none'">

		<!-- Backdrop -->
		<div class="absolute inset-0 bg" @click="open=false"></div>


		<!-- Panel -->
		<div
			x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0 translate-y-3" x-transition:enter-end="opacity-100 translate-y-0" x-transition:leave="transition ease-in duration-150" x-transition:leave-start="opacity-100 translate-y-0" x-transition:leave-end="opacity-0 translate-y-3" class="relative h-full w-full flex flex-col">


			<!-- Top bar -->
			<div class="flex items-center justify-start px-6 pt-5">
				<button
					type="button" class="text-white font-bold inline-flex items-center justify-center rounded-full p-2" aria-label="Menü schließen" @click="open=false">
					<!-- Close icon -->
					Close
				</button>
			</div>

			<!-- Links -->
			<nav class="mt-10 px-6">
				<ul class="space-y-4">
					<li>
						<a @click="open=false" class="block text-white text-2xl font-medium py-3">HOME</a>
					</li>
					<li>
						<a @click="open=false" class="block text-white text-2xl font-medium py-3">STUDIO</a>
					</li>
					<li>
						<a @click="open=false" class="block text-white text-2xl font-medium py-3">SERVICES</a>
					</li>
					<li>
						<a @click="open=false" class="block text-white text-2xl font-medium py-3">PREISE</a>
					</li>
					<li>
						<a @click="open=false" class="block text-white text-2xl font-medium py-3">BURHAN TEKIN</a>
					</li>
					<li>
						<a @click="open=false" class="block text-white text-2xl font-medium py-3">LA BIOSTHETIQUE &copy;</a>
					</li>
				</ul>
			</nav>

			<!-- Footer actions (optional) -->
			<div class="mt-auto px-6 pb-8">
				<div class="flex flex-col gap-3">
					{% include "_partials/call-or-mail.twig" with {
              phone: '+43 987 654 321',
              email: '<EMAIL>',
              class: 'btn btn-outline w-full text-center'
          } %}
				</div>
			</div>
		</div>
	</div>
</header>
