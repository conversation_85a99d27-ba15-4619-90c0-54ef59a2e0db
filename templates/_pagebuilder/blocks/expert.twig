{% import "_macros/image.twig" as m %}
{% set img = mb.image.one() %}

<section
	class="bg-dark text-light relative" data-section="studio">
	<!-- Content -->
	<div
		class="container py-48 max-lg:py-24 max-md:py-16">
		<!-- On desktop we reserve space on the right for the absolute image -->
		<div class="max-w-3xl max-lg:pr-0">
			<h4 class="text-stone-300 max-md:text-sm">{{ mb.subtitle }}</h4>
			<h2 class="mt-2 max-md:text-2xl">{{ mb.title }}</h2>
			<div class="pl-12 mt-12 max-md:pl-0 max-md:mt-6">
				<div class="prose prose-invert max-md:prose-sm">
					{{ mb.body|raw }}
				</div>
			</div>
		</div>
	</div>

	<!-- Image: absolute at desktop, becomes normal flow on mobile -->
	<div
		class="absolute right-0 inset-y-0 w-[50vw] translate-x-1/4 max-xl:translate-x-[12%] max-lg:static max-lg:w-auto max-lg:translate-x-0 max-lg:mt-4">
		<!-- Fade mask only needed when image is absolute over dark bg -->
		<div class="absolute left-0 inset-y-0 w-1/4 bg-gradient-to-r from-dark to-transparent pointer-events-none max-lg:hidden"></div>
		<!-- Keep image cover on desktop, use aspect on mobile for stability -->
		<div class="h-full max-lg:h-auto max-lg:aspect-[4/5]">
			<img class="block h-full w-full object-cover rounded-md max-lg:rounded-none" src="{{ img.getUrl({ width: 1600, height: 2000, mode: 'crop', quality: 82 }) }}" alt="{{ img.title ?: 'Studio image' }}" loading="lazy" decoding="async" sizes="(max-width: 1024px) 100vw, 50vw">
		</div>
	</div>
</section>
