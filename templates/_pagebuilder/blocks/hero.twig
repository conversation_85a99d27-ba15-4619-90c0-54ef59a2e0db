{% import "_macros/image.twig" as m %}
{% set img = mb.backgroundImage.one() %}

<section class="relative isolate w-full flex items-center min-h-[85vh] md:min-h-[92vh] overflow-hidden text-white">
	{% if img %}
		<div class="absolute inset-0 -z-10 scale-125">
			{{ m.img(img, {
        width: 2880,
        transform: 'crop',
        class: 'h-full w-full object-cover object-center'
      }) }}
		</div>
	{% endif %}

	<div class="relative container px-6 py-28 sm:px-8 md:py-36 flex items-center flex-col gap-y-12">
		<img src="/assets/images/la_biosthetique_logo-white.svg" class="h-[48px]"/>
		<img src="/assets/images/burhan_logo.svg"/>
	</div>

	{% if mb.info %}
		<div class="absolute container inset-x-0 bottom-12 md:bottom-12">
			<div class="font-bold mb-2 tracking-widest">INFO</div>
			<p class="font-light tracking-widest">{{ mb.info }}</p>
		</div>
	{% endif %}
</section>
