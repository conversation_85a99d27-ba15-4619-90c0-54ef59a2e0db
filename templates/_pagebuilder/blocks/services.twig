{% set imgs = mb.images ? mb.images.all() : [] %}

<section
	class="relative bg-light overflow-x-hidden" data-section="services">
	<!-- decorative blur layer -->
	<div class="absolute left-0 right-0 inset-y-0 z-10 pointer-events-none max-md:hidden">
		<div class="progressive-blur-container">
			<div class="blur-filter"></div>
			<div class="blur-filter"></div>
			<div class="blur-filter"></div>
			<div class="blur-filter"></div>
			<div class="blur-filter"></div>
			<div class="blur-filter"></div>
			<div class="blur-filter"></div>
			<div class="bg-gradient-to-l from-transparent to-light absolute inset-0"></div>
		</div>
	</div>

	<div class="container relative z-20 pt-48 pb-[330px] max-md:pb-12 max-md:pt-16 pointer-events-none">
		<div class="relative max-w-3xl">
			<div class="relative pointer-events-auto">
				<h4 class="text-stone-500">{{ mb.subtitle }}</h4>
				<h2 class="mt-2 text-5xl font-extrabold tracking-wide text-stone-900 max-w-xl max-md:text-3xl">
					{{ mb.title }}
				</h2>
				<div class="mt-8 pl-12 max-md:pl-0">
					<div class="max-w-prose text-stone-700">{{ mb.body|raw }}</div>
				</div>

				<div class="mt-10 flex items-center gap-24 pl-10 max-md:pl-0 max-md:gap-12">
					<button type="button" data-embla-prev class="h-10 w-10 place-items-center scale-150" aria-label="Vorheriges Bild">
						<svg width="22" height="22" viewbox="0 0 24 24" fill="none" aria-hidden="true">
							<path d="M15 6l-6 6 6 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
						</svg>
					</button>
					<span data-embla-index class="text-2xl font-medium tabular-nums text-stone-900 max-md:text-xl">1/{{ imgs|length }}</span>
					<button type="button" data-embla-next class="h-10 w-10 place-items-center scale-150" aria-label="Nächstes Bild">
						<svg width="22" height="22" viewbox="0 0 24 24" fill="none" aria-hidden="true">
							<path d="M9 18l6-6-6-6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
						</svg>
					</button>
				</div>
			</div>
		</div>
	</div>

	{% if imgs|length %}
		<!-- carousel container raised above decorative layer on desktop, full-bleed on mobile -->
		<div
			class="container absolute left-1/2 inset-y-0 right-0 py-24 max-md:static max-md:w-screen max-md:-mx-[calc(50vw-50%)] max-md:py-8">
			<!-- Embla viewport: must clip & allow vertical page scroll -->
			<div
				data-embla-viewport class="h-full overflow-visible touch-pan-y select-none">
				<!-- Embla track with gap-based spacing (more robust than slide margins) -->
				<div data-embla-track class="flex h-full pl-0 pr-12 gap-12 max-md:px-6 max-md:gap-6">
					{% for img in imgs %}
						<!-- Only REAL slides get this attribute -->
						<div data-embla-slide class="shrink-0 basis-[calc((100%-(2*3rem))/3)] max-md:basis-[80%]">
							<div class="aspect-[3/4] w-full">
								<img class="block h-full w-full rounded-md object-cover pointer-events-none select-none" src="{{ img.getUrl({ width: 1200, height: 1600, mode: 'crop', quality: 82 }) }}" alt="{{ img.title ?: 'Gallery image ' ~ loop.index }}" loading="lazy" decoding="async" draggable="false">
							</div>
						</div>
					{% endfor %}

					<!-- Optional ghosts for visual alignment only (not slides) -->
					<div data-embla-slide aria-hidden="true" class="pointer-events-none shrink-0 basis-[calc((100%-(2*3rem))/3)]"></div>
					<div data-embla-slide aria-hidden="true" class="pointer-events-none shrink-0 basis-[calc((100%-(2*3rem))/3)]"></div>
				</div>
			</div>
		</div>
	{% endif %}
</section>

<style>
	/* tiny safety for legacy/edge cases */
	[data-embla-viewport] {
		-ms-touch-action: pan-y;
		touch-action    : pan-y;
	}
</style>
