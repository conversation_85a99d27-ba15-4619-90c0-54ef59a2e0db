{% set tel = mb.phoneNumber
  ? mb.phoneNumber|replace({' ': '', '/': '', '(': '', ')': ''})
  : null %}
{% set imgs = mb.images ? mb.images.all() : [] %}

<section
	class="bg-dark text-white relative" data-section="studio">
	<!-- TEXT -->
	<div class="container py-24">
		<div class="max-w-4xl">
			<h4>{{ mb.subtitle }}</h4>
			<h2>{{ mb.title }}</h2>
			<div class="pl-12 mt-12">
				<p>{{ mb.body|raw }}</p>
				<p class="mt-8">
					Öffnungszeiten:<br/>
					<strong>{{ mb.openingHours }}</strong>
				</p>
				{% include "_partials/call-or-mail.twig" with {
          phone: '+43 987 654 321',
          email: '<EMAIL>',
          class: 'btn | mt-16 inline-block'
        } %}

				<h3 class="mt-24">{{ mb.locationHeading }}</h3>
				<p class="mt-8">
					{{ mb.addressStreet }}<br/>
					{{ mb.addressCity }}<br/>
					{{ mb.addressCountry }}
				</p>
				<a href="" class="btn | mt-16 inline-block">
					AUF MAPS ÖFFNEN
				</a>
			</div>
		</div>
	</div>

	{% if imgs|length %}
		{# CAROUSEL #}
		<div class="embla w-screen -mx-[calc(50vw-50%)] md:mx-0 md:w-[33vw] md:absolute md:inset-y-0 md:right-0 md:h-full data-[autoplay-ms]:[--autoplay:5000]" data-autoplay-ms="5000">
			<div class="embla__viewport overflow-hidden md:h-full" data-embla-viewport>
				<div class="embla__container flex md:h-full">
					{% for img in imgs %}
						<div
							class="embla__slide basis-full shrink-0">
							{# Mobile: set aspect for nice height; Desktop: fill full height #}
							<img class="block w-full object-cover md:h-full md:w-full aspect-[3/4] md:aspect-auto" src="{{ img.getUrl({ width: 1800, height: 1400, mode: 'crop', quality: 82 }) }}" alt="{{ img.title ?: 'Gallery image ' ~ loop.index }}" loading="lazy" decoding="async">
						</div>
					{% endfor %}
				</div>
			</div>

			{# PROGRESS LINES: in-flow on mobile, overlaid on desktop #}
			<div class="mx-auto flex max-w-[520px] justify-center gap-6 px-6 pointer-events-none absolute inset-x-0 bottom-6 mt-0">
				{% for i in 0..imgs|length-1 %}
					<div class="embla__line relative h-[6px] w-24 overflow-hidden rounded bg-white/30">
						<div class="embla__fill absolute inset-y-0 left-0 w-0 bg-white/90"></div>
					</div>
				{% endfor %}
			</div>
		</div>
	{% endif %}
</section>
