{% set testimonials = mb.testimonials ? mb.testimonials.all() : [] %}
{% set prices = mb.service_prices ? mb.service_prices.all() : [] %}

<section class="relative bg-light" data-section="testimonialsPricelist">
	<div
		class="container py-24 grid grid-cols-2 gap-x-24 max-lg:grid-cols-1 max-lg:gap-x-0 max-lg:gap-y-16 max-md:py-16">
		<!-- Testimonials -->
		<div
			class="bg-dark text-light py-4 pb-12 relative rounded-lg max-md:pb-6">
			<!-- Big quote mark -->
			<p class="text-[185px] leading-[0.8] font-black pl-12 -translate-y-2/5 max-xl:text-[140px] max-lg:text-[120px] max-md:text-[84px] max-md:pl-6 max-md:-translate-y-1/3">„</p>

			<!-- Embla -->
			<div class="embla w-full">
				<div class="embla__viewport w-full overflow-hidden touch-pan-y select-none">
					<div class="embla__container flex w-full">
						{% for testi in testimonials %}
							<div class="embla__slide basis-full w-full shrink-0 px-12">
								<div class="max-w-prose px-0">
									{{ testi.body|raw }}
								</div>
								<p class="mt-12 font-black text-xl max-md:text-lg max-md:mt-8">{{ testi.writer }}</p>
							</div>
						{% endfor %}
					</div>
				</div>
			</div>

			<!-- Controls + fade masks (desktop) -->
			<div
				class="flex items-center justify-end px-12 absolute inset-x-0 bottom-10 max-lg:bottom-6 max-md:static max-md:px-6 max-md:mt-6">
				<!-- Hide gradients on mobile to avoid overlay taps -->
				<div class="bg-gradient-to-r from-transparent to-dark w-1/2 h-full absolute right-0 top-0 max-md:hidden"></div>
				<div class="bg-gradient-to-r from-transparent to-dark w-1/2 h-full absolute right-0 top-0 max-md:hidden"></div>
				<div class="bg-gradient-to-r from-transparent to-dark w-1/2 h-full absolute right-0 top-0 max-md:hidden"></div>

				<div class="flex items-center gap-8 relative z-10">
					<button type="button" data-embla-prev class="size-10 grid place-items-center max-md:size-9" aria-label="Vorheriges Bild">
						<svg width="40" height="40" viewbox="0 0 24 24" fill="none" aria-hidden="true" class="max-md:w-7 max-md:h-7">
							<path d="M15 6l-6 6 6 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
						</svg>
					</button>
					<button type="button" data-embla-next class="size-10 grid place-items-center max-md:size-9" aria-label="Nächstes Bild">
						<svg width="40" height="40" viewbox="0 0 24 24" fill="none" aria-hidden="true" class="max-md:w-7 max-md:h-7">
							<path d="M9 18l6-6-6-6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
						</svg>
					</button>
				</div>
			</div>
		</div>

		<!-- Price list -->
		<div class="max-lg:max-w-3xl">
			<h2 class="mb-12 max-md:mb-8 max-md:text-2xl">{{ mb.title }}</h2>
			{% for price in prices %}
				<div class="border-b border-dark text-dark py-4 flex items-center justify-between gap-6 max-md:py-3">
					<h3 class="!font-medium max-md:text-base">{{ price.title }}</h3>
					<p class="h3 font-black max-md:text-xl">{{ price.price|money }}</p>
				</div>
			{% endfor %}
		</div>
	</div>
</section>

<style>
	/* tiny safety for older touch implementations */
	.embla__viewport {
		-ms-touch-action: pan-y;
		touch-action    : pan-y;
	}
</style>
