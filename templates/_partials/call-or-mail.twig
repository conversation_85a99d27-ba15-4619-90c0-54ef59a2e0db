{% set tz = tz|default('Europe/Vienna') %}
{% set phone = phone|default('+43 123 456 789') %}
{% set email = email|default('<EMAIL>') %}
{% set class = class|default('') %}

{% set times = {
  1: [ { start: 9*60,  end: 18*60 } ],   
  2: [ { start: 9*60,  end: 18*60 } ],
  3: [ { start: 9*60,  end: 18*60 } ],
  4: [ { start: 9*60,  end: 18*60 } ],
  5: [ { start: 9*60,  end: 18*60 } ],
  6: [ { start: 10*60, end: 14*60 } ],   
  7: [ ]                                  
} %}

{% set tag = "now"|date('N', tz) %}
{% set nowMin = ("now"|date('H', tz))*60 + ("now"|date('i', tz)) %}
{% set intervals = times[tag]|default([]) %}
{% set open = false %}
{% for i in intervals %}
	{% if nowMin >= i.start and nowMin < i.end %}
		{% set open = true %}
	{% endif %}
{% endfor %}

{% set telHref = 'tel:' ~ phone|replace({' ': '', '(': '', ')': '', '-': ''}) %}
{% set mailHref = 'mailto:' ~ email %}

<a class="{{ class }}" href="{{ open ? telHref : mailHref }}" aria-label="{{ open ? 'Anrufen' : 'E-Mail schreiben' }}">
	{{ open ? 'Anrufen' : 'E-Mail schreiben' }}
</a>
